"""
# 数据采集模块

模块路径：`sweeper400.measure.draft`

本模块包含NI数据采集卡相关的功能实现。
主要包含AI任务创建和数据采集功能。
"""

import numpy as np
import nidaqmx
from nidaqmx.constants import AcquisitionType, READ_ALL_AVAILABLE
from typing import Union, List, Tuple, Callable, Optional
import threading
import time
from sweeper400.logger import get_logger
from sweeper400.analyze import (
    SamplingInfo,
    Waveform,
    sine_wave_vvi,
    extract_single_tone_information_vvi,
    PositiveFloat,
)

# 获取模块日志器
logger = get_logger(__name__)


def finite_ai_single_channel(channel: str, sampling_info: SamplingInfo) -> Waveform:
    """
    创建有限采样单通道AI任务并返回测量数据

    该函数创建一个有限采样的单通道模拟输入任务，使用机箱的PXIe_CLK100时钟作为参考时钟，
    并返回封装为Waveform对象的测量数据。

    Args:
        channel: AI任务的通道名称，例如 "402Dev2Slot2/ai0"
        sampling_info: 标准化的采样信息字典，包含采样率和采样数

    Returns:
        Waveform: 包含测量数据和元数据的波形对象

    Raises:
        ValueError: 当采样参数无效时
        RuntimeError: 当数据采集失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import finite_ai_single_channel

        # 创建采样信息
        sampling_info = init_sampling_info(1000, 2048)

        # 执行数据采集
        waveform = finite_ai_single_channel("402Dev2Slot2/ai0", sampling_info)

        print(f"采集到 {waveform.samples_num} 个样本")
        print(f"采样率: {waveform.sampling_rate} Hz")
        ```
    """
    logger.info(f"开始创建AI任务 - 通道: {channel}")
    logger.debug(
        f"采样参数 - 采样率: {sampling_info['sampling_rate']} Hz, "
        f"采样数: {sampling_info['samples_num']}"
    )

    try:
        # 创建AI任务
        with nidaqmx.Task() as task:
            # 添加AI电压通道，电压范围设置为±10V
            task.ai_channels.add_ai_voltage_chan(channel, min_val=-10.0, max_val=10.0)
            logger.debug(f"已添加AI通道: {channel}, 电压范围: ±10.0V")

            # 配置采样时钟 - 有限采样模式
            task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            logger.debug(f"已配置采样时钟 - 有限采样模式")

            # 设置参考时钟为PXIe_CLK100
            task.timing.ref_clk_src = "PXIe_Clk100"
            task.timing.ref_clk_rate = 100000000  # 100 MHz
            logger.debug("已设置参考时钟: PXIe_Clk100 (100 MHz)")

            # 启动任务
            task.start()
            logger.debug("AI任务已启动")

            # 读取所有可用数据
            data = task.read(READ_ALL_AVAILABLE)
            logger.info(f"成功读取 {len(data)} 个数据点")

            # 停止任务
            task.stop()
            logger.debug("AI任务已停止")

        # 将数据转换为numpy数组
        data_array = np.array(data, dtype=np.float64)
        logger.debug(f"数据转换完成 - 数组形状: {data_array.shape}")

        # 创建Waveform对象（timestamp参数设为None，将使用当前时间）
        waveform = Waveform(
            data_array, sampling_rate=sampling_info["sampling_rate"], timestamp=None
        )
        logger.info(f"成功创建Waveform对象 - {waveform}")

        return waveform

    except Exception as e:
        logger.error(f"AI任务执行失败: {e}")
        raise RuntimeError(f"数据采集失败: {e}") from e


def finite_ao_single_channel(channel: str, waveform: Waveform) -> int:
    """
    创建有限采样单通道AO任务并输出波形数据

    该函数接收Waveform对象，创建一个有限采样的单通道模拟输出任务，使用机箱的PXIe_CLK100时钟作为参考时钟，
    并将Waveform中的数据通过AO任务输出。

    Args:
        channel: AO任务的通道名称，例如 "402Dev2Slot2/ao0"
        waveform: 包含输出数据和采样信息的Waveform对象

    Returns:
        int: 成功写入的样本数

    Raises:
        ValueError: 当波形参数无效时
        RuntimeError: 当数据输出失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info, sine_wave_vvi
        from sweeper400.measure.draft import finite_ao_single_channel

        # 创建测试波形
        sampling_info = init_sampling_info(1000, 2048)
        test_waveform = sine_wave_vvi(sampling_info, frequency=100.0, amplitude=1.0)

        # 执行数据输出
        samples_written = finite_ao_single_channel("402Dev2Slot2/ao0", test_waveform)

        print(f"成功输出 {samples_written} 个样本")
        ```
    """
    logger.info(f"开始创建AO任务 - 通道: {channel}")
    logger.debug(
        f"波形参数 - 采样率: {waveform.sampling_rate} Hz, "
        f"采样数: {waveform.samples_num}, 形状: {waveform.shape}"
    )

    # 验证波形数据
    if waveform.ndim == 2:
        if waveform.channels_num > 1:
            logger.warning("检测到多通道波形，只使用第一个通道")
            output_data = waveform[0, :].copy()
        else:
            output_data = waveform[0, :].copy()
    else:
        output_data = waveform.copy()

    # 验证数据范围（±10V安全限制）
    data_min, data_max = np.min(output_data), np.max(output_data)
    if data_min < -10.0 or data_max > 10.0:
        logger.error(f"波形数据超出±10V安全范围: [{data_min:.6f}, {data_max:.6f}] V")
        raise ValueError(
            f"波形数据超出±10V安全范围: [{data_min:.6f}, {data_max:.6f}] V"
        )

    logger.debug(f"数据范围检查通过: [{data_min:.6f}, {data_max:.6f}] V")

    try:
        # 创建AO任务
        with nidaqmx.Task() as task:
            # 添加AO电压通道，电压范围设置为±10V
            task.ao_channels.add_ao_voltage_chan(channel, min_val=-10.0, max_val=10.0)
            logger.debug(f"已添加AO通道: {channel}, 电压范围: ±10.0V")

            # 配置采样时钟 - 有限采样模式
            task.timing.cfg_samp_clk_timing(
                rate=waveform.sampling_rate,
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=waveform.samples_num,
            )
            logger.debug("已配置采样时钟 - 有限采样模式")

            # 设置参考时钟为PXIe_CLK100
            task.timing.ref_clk_src = "PXIe_Clk100"
            task.timing.ref_clk_rate = 100000000  # 100 MHz
            logger.debug("已设置参考时钟: PXIe_Clk100 (100 MHz)")

            # 写入数据到缓冲区（不自动启动）
            samples_written = task.write(output_data.tolist(), auto_start=False)
            logger.debug(f"已写入 {samples_written} 个样本到缓冲区")

            # 启动任务
            task.start()
            logger.debug("AO任务已启动")

            # 等待任务完成
            task.wait_until_done()
            logger.debug("AO任务执行完成")

            # 停止任务
            task.stop()
            logger.debug("AO任务已停止")

        logger.info(f"成功输出 {samples_written} 个数据点")
        return samples_written

    except Exception as e:
        logger.error(f"AO任务执行失败: {e}")
        raise RuntimeError(f"数据输出失败: {e}") from e


def get_terminal_name_with_dev_prefix(task: nidaqmx.Task, terminal_name: str) -> str:
    """
    获取带设备前缀的终端名称

    Args:
        task: 指定要获取设备名称的任务
        terminal_name: 指定要获取的终端名称

    Returns:
        带设备前缀的终端名称

    Raises:
        RuntimeError: 当在任务中找不到合适的设备时
    """
    from nidaqmx.constants import ProductCategory

    for device in task.devices:
        if device.product_category not in [
            ProductCategory.C_SERIES_MODULE,
            ProductCategory.SCXI_MODULE,
        ]:
            terminal_name_with_prefix = f"/{device.name}/{terminal_name}"
            logger.debug(f"获取终端名称: {terminal_name_with_prefix}")
            return terminal_name_with_prefix

    raise RuntimeError("在任务中找不到合适的设备")


def synchronized_ai_ao_measurement(
    ai_channel: str,
    ao_channel: str,
    sampling_info: SamplingInfo,
    frequency: PositiveFloat,
    amplitude: PositiveFloat,
    phase: float = 0.0,
) -> Tuple[PositiveFloat, PositiveFloat, float]:
    """
    创建同步的AI-AO测量任务

    该函数创建一对参数相同的有限长单通道AI和AO任务，两任务均使用PXIe_CLK100时钟，
    且通过触发器严格同步开始。AO任务输出指定参数的正弦波，AI任务同步采集数据。
    采集完成后，取AI数据的后半部分进行单频信息提取。

    Args:
        ai_channel: AI任务的通道名称，例如 "402Dev2Slot2/ai0"
        ao_channel: AO任务的通道名称，例如 "402Dev2Slot2/ao0"
        sampling_info: 标准化的采样信息字典，包含采样率和采样数
        frequency: AO输出正弦波的频率（Hz）
        amplitude: AO输出正弦波的幅值（V）
        phase: AO输出正弦波的相位（弧度），默认为0.0

    Returns:
        Tuple[PositiveFloat, PositiveFloat, float]: 检测到的频率、幅值、相位

    Raises:
        ValueError: 当参数无效时
        RuntimeError: 当测量失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import synchronized_ai_ao_measurement

        # 创建采样信息
        sampling_info = init_sampling_info(2000, 4096)

        # 执行同步测量
        freq, amp, phase = synchronized_ai_ao_measurement(
            "402Dev2Slot2/ai0", "402Dev2Slot2/ao0",
            sampling_info, frequency=100.0, amplitude=1.0
        )

        print(f"检测结果: {freq:.2f} Hz, {amp:.4f} V, {phase:.4f} rad")
        ```
    """
    logger.info(f"开始同步AI-AO测量 - AI: {ai_channel}, AO: {ao_channel}")
    logger.debug(
        f"测量参数 - 采样率: {sampling_info['sampling_rate']} Hz, "
        f"采样数: {sampling_info['samples_num']}, "
        f"信号: {frequency} Hz, {amplitude} V, {phase:.4f} rad"
    )

    # 验证幅值范围
    if amplitude > 10.0:
        logger.error(f"信号幅值超出±10V安全范围: {amplitude} V")
        raise ValueError(f"信号幅值超出±10V安全范围: {amplitude} V")

    try:
        # 生成AO输出波形
        ao_waveform = sine_wave_vvi(
            sampling_info, frequency=frequency, amplitude=amplitude, phase=phase
        )
        logger.debug(f"生成AO波形: {ao_waveform}")

        # 创建AI和AO任务
        with nidaqmx.Task() as ai_task, nidaqmx.Task() as ao_task:
            # 配置AI任务
            ai_task.ai_channels.add_ai_voltage_chan(
                ai_channel, min_val=-10.0, max_val=10.0
            )
            ai_task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            ai_task.timing.ref_clk_src = "PXIe_Clk100"
            ai_task.timing.ref_clk_rate = 100000000
            logger.debug("AI任务配置完成")

            # 配置AO任务
            ao_task.ao_channels.add_ao_voltage_chan(
                ao_channel, min_val=-10.0, max_val=10.0
            )
            ao_task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            ao_task.timing.ref_clk_src = "PXIe_Clk100"
            ao_task.timing.ref_clk_rate = 100000000
            logger.debug("AO任务配置完成")

            # 尝试硬件触发同步 - 使用正确的时序引擎终端
            hardware_sync_success = False

            # 方法1: 使用时序引擎StartTrigger终端
            te_terminals = [
                "/402Dev2Slot2/te0/StartTrigger",  # 时序引擎0
                "/402Dev2Slot2/te1/StartTrigger",  # 时序引擎1
                "/402Dev2Slot2/te2/StartTrigger",  # 时序引擎2
                "/402Dev2Slot2/te3/StartTrigger",  # 时序引擎3
            ]

            for te_terminal in te_terminals:
                try:
                    ao_task.triggers.start_trigger.cfg_dig_edge_start_trig(te_terminal)
                    logger.debug(f"配置时序引擎触发同步: {te_terminal}")
                    hardware_sync_success = True
                    break

                except Exception as te_e:
                    logger.debug(f"时序引擎终端 {te_terminal} 失败: {te_e}")
                    continue

            if not hardware_sync_success:
                logger.warning("时序引擎触发方法失败，尝试PXI触发总线")

                # 方法2: 使用PXI触发总线
                pxi_terminals = [
                    "/402Dev2Slot2/PXI_Trig0",
                    "/402Dev2Slot2/PXI_Trig1",
                    "/402Dev2Slot2/PXI_Trig2",
                    "/402Dev2Slot2/PXI_Trig3",
                ]

                for pxi_terminal in pxi_terminals:
                    try:
                        ao_task.triggers.start_trigger.cfg_dig_edge_start_trig(
                            pxi_terminal
                        )
                        logger.debug(f"配置PXI触发总线同步: {pxi_terminal}")
                        hardware_sync_success = True
                        break

                    except Exception as pxi_e:
                        logger.debug(f"PXI触发终端 {pxi_terminal} 失败: {pxi_e}")
                        continue

            if not hardware_sync_success:
                logger.warning("PXI触发总线方法失败，尝试Master-Slave同步模式")

                # 方法3: 备用Master-Slave同步模式
                try:
                    from nidaqmx.constants import SyncType

                    ai_task.triggers.sync_type = SyncType.MASTER
                    ao_task.triggers.sync_type = SyncType.SLAVE

                    # 提交AI任务以生成同步信号
                    ai_task.control(nidaqmx.constants.TaskMode.TASK_COMMIT)

                    logger.debug("配置Master-Slave同步模式")
                    hardware_sync_success = True

                except Exception as e3:
                    logger.warning(f"Master-Slave同步模式失败: {e3}")
                    logger.error("所有硬件同步方法都失败，无法建立硬件触发")
                    raise RuntimeError(
                        f"硬件触发配置失败: 时序引擎触发失败, PXI触发总线失败, Master-Slave模式失败: {e3}"
                    )

            if hardware_sync_success:
                logger.info("成功配置硬件触发同步")

            # 写入AO数据到缓冲区
            ao_task.write(ao_waveform.tolist(), auto_start=False)
            logger.debug("AO数据已写入缓冲区")

            # 启动任务 - 硬件触发同步
            # 先启动AO任务（等待触发），再启动AI任务（产生触发信号）
            ao_task.start()
            logger.debug("AO任务已启动（等待硬件触发）")

            ai_task.start()
            logger.debug("AI任务已启动（产生硬件触发信号，同步启动AO任务）")

            # 读取AI数据
            ai_data = ai_task.read(READ_ALL_AVAILABLE)
            logger.info(f"成功采集 {len(ai_data)} 个AI数据点")

            # 等待AO任务完成
            ao_task.wait_until_done()
            logger.debug("AO任务执行完成")

            # 停止任务
            ai_task.stop()
            ao_task.stop()
            logger.debug("AI和AO任务已停止")

        # 处理AI数据 - 取后半部分
        total_samples = len(ai_data)
        half_point = total_samples // 2
        second_half_data = ai_data[half_point:]
        logger.debug(
            f"提取后半部分数据: {len(second_half_data)} 个样本 (从第 {half_point+1} 个开始)"
        )

        # 创建Waveform对象用于分析
        analysis_waveform = Waveform(
            np.array(second_half_data, dtype=np.float64),
            sampling_rate=sampling_info["sampling_rate"],
            timestamp=None,
        )
        logger.debug(f"创建分析波形: {analysis_waveform}")

        # 提取单频信息
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                analysis_waveform, approx_freq=frequency
            )
        )

        logger.info(
            f"检测结果 - 频率: {detected_freq:.2f} Hz, "
            f"幅值: {detected_amp:.4f} V, 相位: {detected_phase:.4f} rad"
        )

        return detected_freq, detected_amp, detected_phase

    except Exception as e:
        logger.error(f"同步AI-AO测量失败: {e}")
        raise RuntimeError(f"同步测量失败: {e}") from e


class ContinuousAcquisition:
    """
    连续单通道AI数据采集类

    该类实现了基于回调函数的连续数据采集功能，使用机箱的PXIe_CLK100时钟作为参考时钟。
    采集任务在后台持续运行，通过回调函数将数据传递给用户指定的处理函数。
    用户可以随时停止采集任务。

    主要特性：
    - 连续采集模式，无预设结束时间
    - 使用PXIe_CLK100参考时钟确保时钟精度
    - 基于回调函数的数据处理，避免数据丢失
    - 线程安全的启动和停止控制
    - 自动缓冲区管理

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import ContinuousAcquisition

        # 创建采样信息
        sampling_info = init_sampling_info(1000, 1000)  # 1kHz, 每次回调1000样本

        # 定义数据处理函数
        def data_handler(data_chunk, timestamp):
            print(f"收到 {len(data_chunk)} 个样本，时间戳: {timestamp}")

        # 创建连续采集对象
        acq = ContinuousAcquisition("402Dev2Slot2/ai0", sampling_info, data_handler)

        # 开始采集
        acq.start()

        # 运行一段时间后停止
        time.sleep(10)
        acq.stop()

        print(f"总共采集了 {acq.total_samples} 个样本")
        ```
    """

    def __init__(
        self,
        channel: str,
        sampling_info: SamplingInfo,
        data_callback: Callable[[np.ndarray, float], None],
        buffer_size_multiplier: int = 10,
    ):
        """
        初始化连续采集对象

        Args:
            channel: AI通道名称，例如 "402Dev2Slot2/ai0"
            sampling_info: 采样信息，包含采样率和每次回调的样本数
            data_callback: 数据处理回调函数，接收(data_chunk, timestamp)参数
            buffer_size_multiplier: 缓冲区大小倍数，默认为10倍回调样本数

        Raises:
            ValueError: 当参数无效时
        """
        self.channel = channel
        self.sampling_info = sampling_info
        self.data_callback = data_callback
        self.buffer_size_multiplier = buffer_size_multiplier

        # 状态控制
        self._task: Optional[nidaqmx.Task] = None
        self._is_running = False
        self._stop_event = threading.Event()
        self._lock = threading.Lock()

        # 统计信息
        self.total_samples = 0
        self.callback_count = 0
        self.start_time: Optional[float] = None

        # 数据存储（可选）
        self.collected_data: List[np.ndarray] = []
        self.store_data = False  # 默认不存储数据，避免内存溢出

        logger.info(f"创建连续采集对象 - 通道: {channel}")
        logger.debug(
            f"采样参数 - 采样率: {sampling_info['sampling_rate']} Hz, "
            f"回调样本数: {sampling_info['samples_num']}, "
            f"缓冲区倍数: {buffer_size_multiplier}"
        )

    def _callback_function(
        self, task_handle, every_n_samples_event_type, number_of_samples, callback_data
    ) -> int:
        """
        内部回调函数，处理每次采集到的数据

        Args:
            task_handle: 任务句柄
            every_n_samples_event_type: 事件类型
            number_of_samples: 样本数
            callback_data: 回调数据

        Returns:
            int: 返回0表示成功
        """
        try:
            if self._stop_event.is_set():
                return 0

            # 读取数据
            data = self._task.read(number_of_samples_per_channel=number_of_samples)
            data_array = np.array(data, dtype=np.float64)

            # 更新统计信息
            with self._lock:
                self.total_samples += len(data_array)
                self.callback_count += 1
                current_time = time.time()

                # 可选的数据存储
                if self.store_data:
                    self.collected_data.append(data_array.copy())

            # 调用用户回调函数
            try:
                self.data_callback(data_array, current_time)
            except Exception as callback_error:
                logger.error(f"用户回调函数执行失败: {callback_error}")

            # 日志记录（降低频率避免日志过多）
            if self.callback_count % 10 == 0:
                logger.debug(
                    f"回调 #{self.callback_count}: {len(data_array)} 样本, "
                    f"总计: {self.total_samples} 样本"
                )

            return 0

        except Exception as e:
            logger.error(f"回调函数执行失败: {e}")
            self._stop_event.set()  # 发生错误时停止采集
            return -1

    def start(self) -> None:
        """
        开始连续采集任务

        创建并启动nidaqmx任务，配置为连续采集模式，使用PXIe_CLK100参考时钟。

        Raises:
            RuntimeError: 当任务已在运行或启动失败时
        """
        with self._lock:
            if self._is_running:
                logger.warning("采集任务已在运行")
                raise RuntimeError("采集任务已在运行")

            logger.info(f"开始启动连续采集任务 - 通道: {self.channel}")

            try:
                # 创建任务
                self._task = nidaqmx.Task()

                # 添加AI电压通道
                self._task.ai_channels.add_ai_voltage_chan(
                    self.channel, min_val=-10.0, max_val=10.0
                )
                logger.debug(f"已添加AI通道: {self.channel}, 电压范围: ±10.0V")

                # 配置连续采集时钟
                self._task.timing.cfg_samp_clk_timing(
                    rate=self.sampling_info["sampling_rate"],
                    sample_mode=AcquisitionType.CONTINUOUS,
                )
                logger.debug(
                    f"已配置连续采集时钟 - 采样率: {self.sampling_info['sampling_rate']} Hz"
                )

                # 设置参考时钟为PXIe_CLK100
                self._task.timing.ref_clk_src = "PXIe_Clk100"
                self._task.timing.ref_clk_rate = 100000000  # 100 MHz
                logger.debug("已设置参考时钟: PXIe_Clk100 (100 MHz)")

                # 设置输入缓冲区大小
                buffer_size = (
                    self.sampling_info["samples_num"] * self.buffer_size_multiplier
                )
                self._task.in_stream.input_buf_size = buffer_size
                logger.debug(f"已设置输入缓冲区大小: {buffer_size} 样本")

                # 注册回调函数
                self._task.register_every_n_samples_acquired_into_buffer_event(
                    self.sampling_info["samples_num"], self._callback_function
                )
                logger.debug(
                    f"已注册回调函数 - 每 {self.sampling_info['samples_num']} 样本触发一次"
                )

                # 重置状态
                self._stop_event.clear()
                self.total_samples = 0
                self.callback_count = 0
                self.start_time = time.time()
                if self.store_data:
                    self.collected_data.clear()

                # 启动任务
                self._task.start()
                self._is_running = True

                logger.info("连续采集任务已成功启动")

            except Exception as e:
                logger.error(f"启动连续采集任务失败: {e}")
                self._cleanup_task()
                raise RuntimeError(f"启动连续采集任务失败: {e}") from e

    def stop(self) -> None:
        """
        停止连续采集任务

        安全地停止正在运行的采集任务并清理资源。

        Raises:
            RuntimeError: 当停止任务失败时
        """
        with self._lock:
            if not self._is_running:
                logger.warning("采集任务未在运行")
                return

            logger.info("开始停止连续采集任务")

            try:
                # 设置停止事件
                self._stop_event.set()

                # 停止任务
                if self._task is not None:
                    self._task.stop()
                    logger.debug("任务已停止")

                # 清理资源
                self._cleanup_task()

                # 更新状态
                self._is_running = False

                # 记录统计信息
                if self.start_time is not None:
                    duration = time.time() - self.start_time
                    avg_rate = self.total_samples / duration if duration > 0 else 0
                    logger.info(
                        f"连续采集任务已停止 - "
                        f"总样本: {self.total_samples}, "
                        f"回调次数: {self.callback_count}, "
                        f"运行时间: {duration:.2f}s, "
                        f"平均采样率: {avg_rate:.1f} Hz"
                    )

            except Exception as e:
                logger.error(f"停止连续采集任务失败: {e}")
                self._cleanup_task()  # 强制清理
                self._is_running = False
                raise RuntimeError(f"停止连续采集任务失败: {e}") from e

    def _cleanup_task(self) -> None:
        """清理任务资源"""
        if self._task is not None:
            try:
                self._task.close()
                logger.debug("任务资源已清理")
            except Exception as cleanup_error:
                logger.warning(f"清理任务资源时出现警告: {cleanup_error}")
            finally:
                self._task = None

    @property
    def is_running(self) -> bool:
        """检查采集任务是否正在运行"""
        return self._is_running

    def enable_data_storage(self, enable: bool = True) -> None:
        """
        启用或禁用数据存储功能

        Args:
            enable: True启用数据存储，False禁用

        Warning:
            启用数据存储可能导致内存使用量快速增长，请谨慎使用
        """
        with self._lock:
            self.store_data = enable
            if enable:
                logger.warning("已启用数据存储 - 注意内存使用量")
            else:
                logger.info("已禁用数据存储")
                self.collected_data.clear()

    def get_collected_data(self) -> List[np.ndarray]:
        """
        获取已收集的数据（仅在启用数据存储时有效）

        Returns:
            List[np.ndarray]: 收集到的数据块列表
        """
        with self._lock:
            return self.collected_data.copy()

    def get_statistics(self) -> dict:
        """
        获取采集统计信息

        Returns:
            dict: 包含统计信息的字典
        """
        with self._lock:
            stats = {
                "is_running": self._is_running,
                "total_samples": self.total_samples,
                "callback_count": self.callback_count,
                "channel": self.channel,
                "sampling_rate": self.sampling_info["sampling_rate"],
                "samples_per_callback": self.sampling_info["samples_num"],
                "buffer_size_multiplier": self.buffer_size_multiplier,
                "data_storage_enabled": self.store_data,
                "stored_chunks": len(self.collected_data) if self.store_data else 0,
            }

            if self.start_time is not None:
                current_time = time.time()
                duration = current_time - self.start_time
                stats["duration_seconds"] = duration
                stats["average_sampling_rate"] = (
                    self.total_samples / duration if duration > 0 else 0
                )

            return stats

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动停止任务"""
        if self._is_running:
            self.stop()

    def __del__(self):
        """析构函数，确保资源清理"""
        if hasattr(self, "_is_running") and self._is_running:
            try:
                self.stop()
            except Exception:
                pass  # 析构时忽略异常


def continuous_ai_single_channel(
    channel: str,
    sampling_info: SamplingInfo,
    data_callback: Callable[[np.ndarray, float], None],
    duration_seconds: Optional[float] = None,
    enable_data_storage: bool = False,
) -> List[np.ndarray]:
    """
    简化的连续单通道AI数据采集函数

    这是ContinuousAcquisition类的简化封装，提供更直接的函数式接口。
    适用于简单的连续采集任务，自动处理启动、运行和停止流程。

    Args:
        channel: AI通道名称，例如 "402Dev2Slot2/ai0"
        sampling_info: 采样信息，包含采样率和每次回调的样本数
        data_callback: 数据处理回调函数，接收(data_chunk, timestamp)参数
        duration_seconds: 采集持续时间（秒），None表示需要手动停止
        enable_data_storage: 是否启用数据存储功能

    Returns:
        List[np.ndarray]: 如果启用了数据存储，返回收集到的数据块列表；否则返回空列表

    Raises:
        RuntimeError: 当采集失败时
        KeyboardInterrupt: 当用户中断时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import continuous_ai_single_channel

        # 创建采样信息
        sampling_info = init_sampling_info(1000, 1000)

        # 定义数据处理函数
        def process_data(data, timestamp):
            print(f"收到 {len(data)} 样本，均值: {np.mean(data):.6f}V")

        # 运行5秒的连续采集
        data_chunks = continuous_ai_single_channel(
            "402Dev2Slot2/ai0",
            sampling_info,
            process_data,
            duration_seconds=5.0,
            enable_data_storage=True
        )

        print(f"收集了 {len(data_chunks)} 个数据块")
        ```
    """
    logger.info(f"开始简化连续采集 - 通道: {channel}, 持续时间: {duration_seconds}s")

    # 创建连续采集对象
    acq = ContinuousAcquisition(
        channel=channel,
        sampling_info=sampling_info,
        data_callback=data_callback,
    )

    try:
        # 启用数据存储（如果需要）
        if enable_data_storage:
            acq.enable_data_storage(True)

        # 启动采集
        acq.start()
        logger.info("连续采集已启动")

        if duration_seconds is not None:
            # 定时采集
            logger.info(f"将运行 {duration_seconds} 秒")
            time.sleep(duration_seconds)
        else:
            # 手动停止采集
            logger.info("连续采集运行中，按 Ctrl+C 停止")
            try:
                while True:
                    time.sleep(0.1)
            except KeyboardInterrupt:
                logger.info("用户中断，停止采集")

        # 停止采集
        acq.stop()

        # 获取统计信息
        stats = acq.get_statistics()
        logger.info(
            f"采集完成 - 总样本: {stats['total_samples']}, "
            f"回调次数: {stats['callback_count']}, "
            f"运行时间: {stats.get('duration_seconds', 0):.2f}s"
        )

        # 返回收集的数据（如果启用了存储）
        if enable_data_storage:
            return acq.get_collected_data()
        else:
            return []

    except Exception as e:
        logger.error(f"连续采集失败: {e}")
        if acq.is_running:
            acq.stop()
        raise RuntimeError(f"连续采集失败: {e}") from e
