# 连续数据采集功能使用说明

本文档介绍如何使用sweeper400包中新增的连续数据采集功能。

## 功能概述

连续数据采集功能提供了两种使用方式：

1. **ContinuousAcquisition类** - 面向对象的方式，提供完整的控制功能
2. **continuous_ai_single_channel函数** - 简化的函数式接口，适用于简单场景

## 主要特性

- ✅ 连续采集模式，无预设结束时间
- ✅ 使用PXIe_CLK100参考时钟确保时钟精度
- ✅ 基于回调函数的数据处理，避免数据丢失
- ✅ 线程安全的启动和停止控制
- ✅ 自动缓冲区管理
- ✅ 可选的数据存储功能
- ✅ 详细的统计信息和日志记录

## 使用方法

### 方法1：使用ContinuousAcquisition类

```python
import time
import numpy as np
from sweeper400.analyze import init_sampling_info
from sweeper400.measure import ContinuousAcquisition

# 创建采样信息
sampling_info = init_sampling_info(1000, 1000)  # 1kHz, 每次回调1000样本

# 定义数据处理函数
def data_handler(data_chunk: np.ndarray, timestamp: float) -> None:
    mean_val = np.mean(data_chunk)
    print(f"收到 {len(data_chunk)} 样本，均值: {mean_val:.6f}V")

# 创建连续采集对象
acq = ContinuousAcquisition(
    channel="402Dev2Slot2/ai0",
    sampling_info=sampling_info,
    data_callback=data_handler,
    buffer_size_multiplier=10  # 10倍缓冲区
)

try:
    # 启动采集
    acq.start()
    print("采集已启动...")
    
    # 运行10秒
    time.sleep(10)
    
    # 获取统计信息
    stats = acq.get_statistics()
    print(f"统计信息: {stats}")
    
finally:
    # 停止采集
    acq.stop()
```

### 方法2：使用上下文管理器（推荐）

```python
# 使用上下文管理器自动管理资源
with ContinuousAcquisition(
    channel="402Dev2Slot2/ai0",
    sampling_info=sampling_info,
    data_callback=data_handler,
) as acq:
    
    # 启用数据存储（可选）
    acq.enable_data_storage(True)
    
    # 启动采集
    acq.start()
    
    # 运行一段时间
    time.sleep(5)
    
    # 获取存储的数据
    collected_data = acq.get_collected_data()
    print(f"收集了 {len(collected_data)} 个数据块")
    
    # 上下文管理器会自动停止采集
```

### 方法3：使用简化函数

```python
from sweeper400.measure import continuous_ai_single_channel

# 定义数据处理函数
def process_data(data: np.ndarray, timestamp: float) -> None:
    print(f"处理 {len(data)} 样本，时间戳: {timestamp:.3f}")

# 运行5秒的连续采集，启用数据存储
data_chunks = continuous_ai_single_channel(
    channel="402Dev2Slot2/ai0",
    sampling_info=sampling_info,
    data_callback=process_data,
    duration_seconds=5.0,
    enable_data_storage=True
)

print(f"收集了 {len(data_chunks)} 个数据块")
```

## 参数说明

### 采样信息 (SamplingInfo)

```python
from sweeper400.analyze import init_sampling_info

# 创建采样信息
sampling_info = init_sampling_info(
    sampling_rate=1000,    # 采样率 (Hz)
    samples_num=1000       # 每次回调的样本数
)
```

### 通道名称

根据实际硬件配置使用正确的通道名称：
- `"402Dev2Slot2/ai0"` - 第一张板卡的第一个AI通道
- `"402Dev2Slot2/ai1"` - 第一张板卡的第二个AI通道
- `"402Dev2Slot3/ai0"` - 第二张板卡的第一个AI通道
- `"402Dev2Slot3/ai1"` - 第二张板卡的第二个AI通道

### 回调函数

回调函数必须接收两个参数：
- `data_chunk: np.ndarray` - 采集到的数据块
- `timestamp: float` - 时间戳

```python
def my_callback(data_chunk: np.ndarray, timestamp: float) -> None:
    # 处理数据
    mean_val = np.mean(data_chunk)
    std_val = np.std(data_chunk)
    print(f"均值: {mean_val:.6f}V, 标准差: {std_val:.6f}V")
```

## 注意事项

1. **内存使用**: 启用数据存储功能时要注意内存使用量，长时间采集可能导致内存不足
2. **回调函数性能**: 回调函数应该尽快执行完毕，避免阻塞数据采集
3. **异常处理**: 回调函数中的异常会被捕获并记录，但不会中断采集
4. **资源清理**: 使用上下文管理器或确保调用stop()方法来清理资源

## 测试文件

项目中包含以下测试文件供参考：

- `tests/test_continuous_acquisition.py` - 完整的功能测试
- `tests/demo_continuous_acquisition.py` - 使用演示
- `tests/test_simple_continuous.py` - 简化函数测试
- `tests/test_package_import.py` - 包导入测试

## 故障排除

1. **采集无法启动**: 检查通道名称是否正确，NI机箱是否正常连接
2. **数据丢失**: 增加buffer_size_multiplier参数值
3. **采样率不准确**: 检查PXIe_CLK100时钟是否正常工作
4. **内存不足**: 禁用数据存储功能或定期清理collected_data

## 技术细节

- 使用nidaqmx的连续采集模式 (AcquisitionType.CONTINUOUS)
- 参考时钟: PXIe_CLK100 (100 MHz)
- 电压范围: ±10.0V
- 线程安全的状态管理
- 自动缓冲区大小计算
