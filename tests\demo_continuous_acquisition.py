"""
连续数据采集演示脚本

本脚本演示如何使用ContinuousAcquisition类进行连续数据采集。
这是一个简单的使用示例，展示了基本的启动、运行和停止流程。

运行前确保：
1. NI机箱已正确连接
2. conda环境已激活为"sweep"
3. 通道名称与实际硬件配置匹配
"""

import time
import numpy as np
from sweeper400.analyze import init_sampling_info
from sweeper400.measure.draft import ContinuousAcquisition
from sweeper400.logger import get_logger

# 获取日志器
logger = get_logger(__name__)


def simple_demo():
    """简单的连续采集演示"""
    print("=== 连续数据采集演示 ===")
    print("这个演示将运行10秒的连续数据采集")
    print("按 Ctrl+C 可以提前停止")
    
    # 创建采样信息：1kHz采样率，每次回调1000样本
    sampling_info = init_sampling_info(1000, 1000)
    print(f"采样配置: 采样率 {sampling_info['sampling_rate']} Hz, 每次回调 {sampling_info['samples_num']} 样本")
    
    # 数据统计变量
    data_stats = {
        'callback_count': 0,
        'min_voltage': float('inf'),
        'max_voltage': float('-inf'),
        'total_samples': 0
    }
    
    def data_processor(data_chunk: np.ndarray, timestamp: float) -> None:
        """处理每次采集到的数据"""
        data_stats['callback_count'] += 1
        data_stats['total_samples'] += len(data_chunk)
        
        # 计算统计信息
        chunk_min = np.min(data_chunk)
        chunk_max = np.max(data_chunk)
        chunk_mean = np.mean(data_chunk)
        chunk_std = np.std(data_chunk)
        
        # 更新全局统计
        data_stats['min_voltage'] = min(data_stats['min_voltage'], chunk_min)
        data_stats['max_voltage'] = max(data_stats['max_voltage'], chunk_max)
        
        # 打印当前数据信息
        print(f"回调 #{data_stats['callback_count']:2d}: "
              f"均值={chunk_mean:+.6f}V, 标准差={chunk_std:.6f}V, "
              f"范围=[{chunk_min:+.6f}, {chunk_max:+.6f}]V")
    
    # 创建连续采集对象
    acq = ContinuousAcquisition(
        channel="402Dev2Slot2/ai0",  # 使用第一个AI通道
        sampling_info=sampling_info,
        data_callback=data_processor,
        buffer_size_multiplier=10  # 10倍缓冲区，确保不丢数据
    )
    
    try:
        # 启动采集
        print("\n启动连续采集...")
        acq.start()
        print("✓ 采集已启动，开始接收数据")
        
        # 运行10秒
        start_time = time.time()
        try:
            while time.time() - start_time < 10:
                time.sleep(0.1)  # 短暂休眠，避免CPU占用过高
        except KeyboardInterrupt:
            print("\n用户中断，停止采集...")
        
        # 停止采集
        print("\n停止采集...")
        acq.stop()
        print("✓ 采集已停止")
        
        # 显示最终统计信息
        final_stats = acq.get_statistics()
        print(f"\n=== 采集统计 ===")
        print(f"运行时间: {final_stats.get('duration_seconds', 0):.2f} 秒")
        print(f"总样本数: {final_stats['total_samples']}")
        print(f"回调次数: {final_stats['callback_count']}")
        print(f"平均采样率: {final_stats.get('average_sampling_rate', 0):.1f} Hz")
        print(f"电压范围: [{data_stats['min_voltage']:+.6f}, {data_stats['max_voltage']:+.6f}] V")
        
        print("\n演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        if acq.is_running:
            acq.stop()
        raise


def advanced_demo():
    """高级演示：使用数据存储功能"""
    print("\n=== 高级演示：数据存储功能 ===")
    print("这个演示将采集5秒数据并存储在内存中")
    
    # 创建采样信息：2kHz采样率，每次回调500样本
    sampling_info = init_sampling_info(2000, 500)
    
    def simple_processor(data_chunk: np.ndarray, timestamp: float) -> None:
        """简单的数据处理器"""
        print(f"收到 {len(data_chunk)} 样本，时间戳: {timestamp:.3f}")
    
    # 使用上下文管理器自动管理资源
    with ContinuousAcquisition(
        channel="402Dev2Slot2/ai0",
        sampling_info=sampling_info,
        data_callback=simple_processor,
    ) as acq:
        
        # 启用数据存储
        acq.enable_data_storage(True)
        print("✓ 已启用数据存储")
        
        # 启动采集
        acq.start()
        print("✓ 采集已启动")
        
        # 运行5秒
        time.sleep(5)
        
        # 获取存储的数据
        collected_data = acq.get_collected_data()
        print(f"\n收集到 {len(collected_data)} 个数据块")
        
        if collected_data:
            # 合并所有数据
            all_data = np.concatenate(collected_data)
            print(f"总数据点: {len(all_data)}")
            print(f"数据统计: 均值={np.mean(all_data):.6f}V, 标准差={np.std(all_data):.6f}V")
            print(f"数据范围: [{np.min(all_data):.6f}, {np.max(all_data):.6f}]V")
        
        # 上下文管理器会自动停止采集
        print("✓ 采集已自动停止")
    
    print("高级演示完成！")


def main():
    """主函数"""
    try:
        # 基本演示
        simple_demo()
        
        # 等待一下
        time.sleep(2)
        
        # 高级演示
        advanced_demo()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        logger.error(f"演示失败: {e}")
        print(f"❌ 演示失败: {e}")


if __name__ == "__main__":
    main()
