"""
测试连续数据采集功能

本测试文件用于验证ContinuousAcquisition类的功能，包括：
- 连续采集任务的启动和停止
- 回调函数的正确执行
- 数据存储功能
- 统计信息的准确性
- 异常处理

运行前确保：
1. NI机箱已正确连接
2. conda环境已激活为"sweep"
3. 通道名称与实际硬件配置匹配
"""

import time
import numpy as np
from sweeper400.analyze import init_sampling_info
from sweeper400.measure.draft import ContinuousAcquisition
from sweeper400.logger import get_logger

# 获取测试日志器
logger = get_logger(__name__)


def test_basic_continuous_acquisition():
    """测试基本的连续采集功能"""
    logger.info("=== 开始基本连续采集测试 ===")
    
    # 创建采样信息：1kHz采样率，每次回调1000样本（即每秒1次回调）
    sampling_info = init_sampling_info(1000, 1000)
    logger.info(f"采样配置: {sampling_info}")
    
    # 数据处理计数器
    callback_counter = 0
    received_data_lengths = []
    
    def data_handler(data_chunk: np.ndarray, timestamp: float) -> None:
        """数据处理回调函数"""
        nonlocal callback_counter
        callback_counter += 1
        received_data_lengths.append(len(data_chunk))
        
        # 计算数据的基本统计信息
        data_mean = np.mean(data_chunk)
        data_std = np.std(data_chunk)
        data_min = np.min(data_chunk)
        data_max = np.max(data_chunk)
        
        logger.info(
            f"回调 #{callback_counter}: 收到 {len(data_chunk)} 样本, "
            f"均值: {data_mean:.6f}V, 标准差: {data_std:.6f}V, "
            f"范围: [{data_min:.6f}, {data_max:.6f}]V, "
            f"时间戳: {timestamp:.3f}"
        )
    
    # 创建连续采集对象
    acq = ContinuousAcquisition(
        channel="402Dev2Slot2/ai0",  # 使用第一个通道
        sampling_info=sampling_info,
        data_callback=data_handler,
        buffer_size_multiplier=5  # 5倍缓冲区
    )
    
    try:
        # 检查初始状态
        assert not acq.is_running, "初始状态应该是未运行"
        logger.info("✓ 初始状态检查通过")
        
        # 启动采集
        acq.start()
        assert acq.is_running, "启动后应该是运行状态"
        logger.info("✓ 采集任务已启动")
        
        # 运行5秒，应该收到大约5次回调
        logger.info("运行5秒，观察数据采集...")
        time.sleep(5)
        
        # 检查统计信息
        stats = acq.get_statistics()
        logger.info(f"统计信息: {stats}")
        
        # 验证回调次数合理（允许±1的误差）
        expected_callbacks = 5  # 5秒，每秒1次回调
        assert abs(callback_counter - expected_callbacks) <= 1, \
            f"回调次数异常: 期望约{expected_callbacks}次，实际{callback_counter}次"
        logger.info(f"✓ 回调次数检查通过: {callback_counter}次")
        
        # 验证数据长度一致性
        assert all(length == 1000 for length in received_data_lengths), \
            f"数据长度不一致: {received_data_lengths}"
        logger.info("✓ 数据长度一致性检查通过")
        
        # 验证总样本数
        expected_total = callback_counter * 1000
        assert stats["total_samples"] == expected_total, \
            f"总样本数不匹配: 期望{expected_total}，实际{stats['total_samples']}"
        logger.info(f"✓ 总样本数检查通过: {stats['total_samples']}")
        
        # 停止采集
        acq.stop()
        assert not acq.is_running, "停止后应该是未运行状态"
        logger.info("✓ 采集任务已停止")
        
        # 获取最终统计信息
        final_stats = acq.get_statistics()
        logger.info(f"最终统计信息: {final_stats}")
        
        logger.info("=== 基本连续采集测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        if acq.is_running:
            acq.stop()
        raise


def test_data_storage_feature():
    """测试数据存储功能"""
    logger.info("=== 开始数据存储功能测试 ===")
    
    # 创建采样信息：2kHz采样率，每次回调500样本（即每0.25秒1次回调）
    sampling_info = init_sampling_info(2000, 500)
    
    def simple_handler(data_chunk: np.ndarray, timestamp: float) -> None:
        """简单的数据处理函数"""
        logger.debug(f"收到数据: {len(data_chunk)} 样本")
    
    # 使用上下文管理器
    with ContinuousAcquisition(
        channel="402Dev2Slot2/ai0",
        sampling_info=sampling_info,
        data_callback=simple_handler,
    ) as acq:
        
        # 启用数据存储
        acq.enable_data_storage(True)
        logger.info("✓ 已启用数据存储")
        
        # 启动采集
        acq.start()
        logger.info("✓ 采集已启动")
        
        # 运行2秒
        time.sleep(2)
        
        # 检查存储的数据
        collected_data = acq.get_collected_data()
        logger.info(f"收集到 {len(collected_data)} 个数据块")
        
        # 验证数据存储
        assert len(collected_data) > 0, "应该有存储的数据"
        assert all(isinstance(chunk, np.ndarray) for chunk in collected_data), \
            "所有数据块都应该是numpy数组"
        assert all(len(chunk) == 500 for chunk in collected_data), \
            "所有数据块长度都应该是500"
        
        logger.info("✓ 数据存储功能验证通过")
        
        # 禁用数据存储
        acq.enable_data_storage(False)
        logger.info("✓ 已禁用数据存储")
        
        # 验证数据已清空
        empty_data = acq.get_collected_data()
        assert len(empty_data) == 0, "禁用存储后数据应该被清空"
        logger.info("✓ 数据清空验证通过")
    
    logger.info("=== 数据存储功能测试完成 ===")


def test_error_handling():
    """测试错误处理"""
    logger.info("=== 开始错误处理测试 ===")
    
    sampling_info = init_sampling_info(1000, 1000)
    
    def error_handler(data_chunk: np.ndarray, timestamp: float) -> None:
        """会抛出异常的处理函数"""
        if len(data_chunk) > 0:  # 第一次回调时抛出异常
            raise ValueError("测试异常")
    
    acq = ContinuousAcquisition(
        channel="402Dev2Slot2/ai0",
        sampling_info=sampling_info,
        data_callback=error_handler,
    )
    
    try:
        # 测试重复启动
        acq.start()
        try:
            acq.start()  # 应该抛出异常
            assert False, "重复启动应该抛出异常"
        except RuntimeError:
            logger.info("✓ 重复启动异常处理正确")
        
        # 停止任务
        acq.stop()
        
        # 测试重复停止（应该不抛出异常）
        acq.stop()
        logger.info("✓ 重复停止处理正确")
        
    except Exception as e:
        logger.error(f"错误处理测试失败: {e}")
        if acq.is_running:
            acq.stop()
        raise
    
    logger.info("=== 错误处理测试完成 ===")


def main():
    """主测试函数"""
    logger.info("开始连续采集功能测试")
    
    try:
        # 基本功能测试
        test_basic_continuous_acquisition()
        
        # 数据存储功能测试
        test_data_storage_feature()
        
        # 错误处理测试
        test_error_handling()
        
        logger.info("🎉 所有测试通过！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
