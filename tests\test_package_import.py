"""
测试包级别导入功能

验证新的连续采集功能可以从包级别正确导入和使用。
"""

import time
import numpy as np
from sweeper400.analyze import init_sampling_info
from sweeper400.measure import ContinuousAcquisition, continuous_ai_single_channel
from sweeper400.logger import get_logger

# 获取日志器
logger = get_logger(__name__)


def test_package_level_import():
    """测试包级别导入"""
    logger.info("=== 测试包级别导入功能 ===")
    
    # 测试类导入
    sampling_info = init_sampling_info(1000, 500)
    
    def simple_callback(data: np.ndarray, timestamp: float) -> None:
        print(f"类方式: 收到 {len(data)} 样本")
    
    # 使用类方式
    print("测试ContinuousAcquisition类...")
    with ContinuousAcquisition(
        channel="402Dev2Slot2/ai0",
        sampling_info=sampling_info,
        data_callback=simple_callback,
    ) as acq:
        acq.start()
        time.sleep(2)
        stats = acq.get_statistics()
        print(f"类方式统计: {stats['total_samples']} 样本, {stats['callback_count']} 次回调")
    
    print("✓ 类导入测试通过")
    
    # 测试函数导入
    def function_callback(data: np.ndarray, timestamp: float) -> None:
        print(f"函数方式: 收到 {len(data)} 样本")
    
    print("\n测试continuous_ai_single_channel函数...")
    collected_data = continuous_ai_single_channel(
        channel="402Dev2Slot2/ai0",
        sampling_info=sampling_info,
        data_callback=function_callback,
        duration_seconds=2.0,
        enable_data_storage=True
    )
    
    print(f"函数方式收集: {len(collected_data)} 个数据块")
    print("✓ 函数导入测试通过")
    
    logger.info("=== 包级别导入测试完成 ===")


def main():
    """主测试函数"""
    try:
        test_package_level_import()
        print("\n🎉 包级别导入测试通过！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
