"""
测试简化的连续采集函数

本测试文件验证continuous_ai_single_channel函数的功能。
"""

import numpy as np
from sweeper400.analyze import init_sampling_info
from sweeper400.measure.draft import continuous_ai_single_channel
from sweeper400.logger import get_logger

# 获取日志器
logger = get_logger(__name__)


def test_simple_continuous_function():
    """测试简化的连续采集函数"""
    logger.info("=== 测试简化连续采集函数 ===")
    
    # 创建采样信息
    sampling_info = init_sampling_info(1000, 500)  # 1kHz, 每次500样本
    
    # 数据统计
    callback_count = 0
    total_samples = 0
    
    def data_processor(data_chunk: np.ndarray, timestamp: float) -> None:
        """数据处理函数"""
        nonlocal callback_count, total_samples
        callback_count += 1
        total_samples += len(data_chunk)
        
        mean_val = np.mean(data_chunk)
        std_val = np.std(data_chunk)
        
        print(f"回调 #{callback_count}: {len(data_chunk)} 样本, "
              f"均值: {mean_val:.6f}V, 标准差: {std_val:.6f}V")
    
    # 运行3秒的连续采集，启用数据存储
    print("开始3秒连续采集...")
    collected_data = continuous_ai_single_channel(
        channel="402Dev2Slot2/ai0",
        sampling_info=sampling_info,
        data_callback=data_processor,
        duration_seconds=3.0,
        enable_data_storage=True
    )
    
    # 验证结果
    print(f"\n采集结果:")
    print(f"回调次数: {callback_count}")
    print(f"总样本数: {total_samples}")
    print(f"收集的数据块: {len(collected_data)}")
    
    if collected_data:
        # 合并所有数据进行分析
        all_data = np.concatenate(collected_data)
        print(f"合并后总数据点: {len(all_data)}")
        print(f"数据统计: 均值={np.mean(all_data):.6f}V, 标准差={np.std(all_data):.6f}V")
        print(f"数据范围: [{np.min(all_data):.6f}, {np.max(all_data):.6f}]V")
    
    # 验证数据一致性
    assert callback_count > 0, "应该有回调执行"
    assert total_samples > 0, "应该有样本采集"
    assert len(collected_data) == callback_count, "数据块数量应该等于回调次数"
    
    print("✓ 简化连续采集函数测试通过")


def main():
    """主测试函数"""
    try:
        test_simple_continuous_function()
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
